import os
import time
import sys
from datetime import datetime
from multiprocessing import Process

import pandas as pd

from TqMockApi import TqMockA<PERSON>
from constant.constant import TQ_TIME_OUT, CONFIG_FILE_NAME, RUN_NUM, STATUS, GOING_ON, PID, PARAM_DF_FILE_NAME, CAT, \
    ACCOUNT, CODE, USE_API_CODE, DRY_RUN, PARAM_VERSION, BACKTEST_START_TIME, BACKTEST_END_TIME, \
    PRICE_TIME_SEQ_FILE_NAME, RUN_LOG_FILE_NAME, BACKTEST_CHECKPOINT_DIR, TIME_FORMAT, DATE_FORMAT, \
    CHECKPOINT_DIR, READABLE_TIME_FORMAT, TOTAL_FUND_FOR_ALL_INSTRUCT, TASK_START_TIME, PENDING, VAR_DIR, \
    GLOBAL_CONFIG_FILE_NAME, ACCOUNT_CONFIG_FILE_NAME, ACCOUNT_NAME_LIST, CHOOSE_CAT_LIST, TASK_UPDATE_TIME, PATH, \
    INIT_FUND, OPEN_ONCE_MAX_FUND, MESSAGE, GOING_ON_OVER, SUCCESS, SUCCESS_OVER, PARAM_INSTANCE_CHART_FILE_NAME, \
    TIME_COL, PRICE_COL, PARAM_INSTANCE_DF_FILE_NAME, FAILED, ACCOUNT_T, PASSWORD_T
from entity.run import PrepareCheckpointParam
from entity.trade import TradeResult, TradeInput
from entity.trade_base import TradeMode
from entity.param import Param
from loader.checkpoint_loader import load_config
from new_trade.base_trade_executor import BaseTradeExecutor
from new_trade.mock_trade_executor import MockTradeExecutor
from new_trade.tq_trade_executor import TqTradeExecutor
from new_trade.trade_strategy import TradeStrategy
from new_trade.visualize import draw_line
from util.common_util import get_code_version, is_empty, get_caller, print_warn
from util.date_util import get_cur_time_str
from util.email_util import send_warn_email
from util.encrypt_util import decrypt
from util.file_util import join_path, load_json, update_json_file, mkdir_if_not_exists, is_invalid_path, \
    get_absolute_path, get_relative_path, dump_json, copy_to_dir, get_param_root_dir, get_latest_param_dir
from util.process_util import FilePrintRunner, is_process_running
from util.tq_util import get_code_from_tq
from util.trade_util import get_time_price_list, get_instance_list, merge_to_instance_df

try:
    from tqsdk2 import TqApi, TqAuth, TqSim, TqBacktest, TargetPosTask, BacktestFinished, TqAccount
except ImportError:
    try:
        from tqsdk import TqApi, TqAuth, TqSim, TqBacktest, TargetPosTask, BacktestFinished, TqAccount
    except ImportError:
        print("Error: Neither tqsdk2 nor tqsdk could be imported. Please install one of them.")
        sys.exit(1)


def get_ckp_path(data_dir, now, cat, dry_run, account_name):
    if dry_run:
        return join_path(data_dir, BACKTEST_CHECKPOINT_DIR, account_name, cat, now.strftime(DATE_FORMAT),
                         now.strftime(TIME_FORMAT))

    return join_path(data_dir, CHECKPOINT_DIR, account_name, cat, now.strftime(DATE_FORMAT),
                     now.strftime(TIME_FORMAT))


def prepare(data_dir, now, cat, dry_run, ckp_path, start_time_str, end_time_str, use_backtest_param, account_name):
    mkdir_if_not_exists(ckp_path)
    prepare_param = PrepareCheckpointParam()
    prepare_param.cat = cat
    cat_param_dir = join_path(get_param_root_dir(data_dir, use_backtest_param, account_name), cat)
    prepare_param.meta_param_path = get_latest_param_dir(cat_param_dir)
    if is_invalid_path(prepare_param.meta_param_path):
        print(f"跳过尚未配置的品种：{cat}")
        return False

    use_backtest_param_suffix = "（回测参数）" if use_backtest_param else ""
    print(f"使用的参数={os.path.basename(prepare_param.meta_param_path)}" + use_backtest_param_suffix)
    prepare_param.start_time_str = now.strftime(READABLE_TIME_FORMAT)
    prepare_param.backtest_start_time_str = start_time_str
    prepare_param.backtest_end_time_str = end_time_str
    prepare_param.checkpoint_path = get_relative_path(ckp_path, data_dir)
    prepare_param.dry_run = dry_run
    prepare_checkpoint(prepare_param, data_dir)
    return True


def prepare_checkpoint(prepare_param: PrepareCheckpointParam, data_dir):
    checkpoint_path = get_absolute_path(prepare_param.checkpoint_path, data_dir)
    meta_param = load_config(prepare_param.meta_param_path)
    if is_empty(meta_param):
        raise NotImplementedError(f"记得实现这里: {get_caller()}")

    account_name = meta_param[ACCOUNT]
    global_config_path = join_path(data_dir, VAR_DIR, account_name, GLOBAL_CONFIG_FILE_NAME)
    if is_invalid_path(global_config_path):
        raise RuntimeError(f"{global_config_path} not found")

    global_config_dict = load_json(global_config_path, with_lock=True, data_dir=data_dir)
    if TOTAL_FUND_FOR_ALL_INSTRUCT not in global_config_dict:
        raise RuntimeError(f"{TOTAL_FUND_FOR_ALL_INSTRUCT} not found in {GLOBAL_CONFIG_FILE_NAME}")

    total_fund_for_all_instruct = global_config_dict[TOTAL_FUND_FOR_ALL_INSTRUCT]
    # copy param df
    param_df_path = join_path(prepare_param.meta_param_path, PARAM_DF_FILE_NAME)
    copy_to_dir(param_df_path, checkpoint_path)

    # copy account info from var dir
    var_dir = join_path(data_dir, VAR_DIR, account_name)
    account_config_path = join_path(var_dir, ACCOUNT_CONFIG_FILE_NAME)
    copy_to_dir(account_config_path, checkpoint_path)

    # other config
    run_config_dict = {
        TOTAL_FUND_FOR_ALL_INSTRUCT: total_fund_for_all_instruct,
        TASK_START_TIME: prepare_param.start_time_str,
        STATUS: PENDING,
        DRY_RUN: prepare_param.dry_run,
        RUN_NUM: 0,
        BACKTEST_START_TIME: prepare_param.backtest_start_time_str,
        BACKTEST_END_TIME: prepare_param.backtest_end_time_str
    }
    for key in meta_param:
        run_config_dict[key] = meta_param[key]
    config_path = join_path(checkpoint_path, CONFIG_FILE_NAME)
    dump_json(run_config_dict, config_path)


def linear_start_backtest_process(data_dir, account_cat_list, project_dir, start_time_str, end_time_str,
                                  use_backtest_param, send_email):
    ckp_path_list = list()
    now = datetime.now()
    for d in account_cat_list:
        cat = d[CAT]
        account_name = d[ACCOUNT]
        ckp_path = get_ckp_path(data_dir, now, cat, True, account_name)
        prepare_success = prepare(data_dir, now, cat, True, ckp_path, start_time_str, end_time_str,
                                  use_backtest_param, account_name)
        if not prepare_success:
            continue

        ckp_path_list.append(ckp_path)
        print(f"✅已开启风控：{cat}")
    if is_empty(ckp_path_list):
        print(f"没有可执行的风控任务，请检查参数是否正确配置")
        return

    log_path = join_path(ckp_path_list[0], RUN_LOG_FILE_NAME)
    runner = FilePrintRunner(linear_adapt_to_run_risk, log_path)
    process = Process(target=runner, args=(ckp_path_list, data_dir, project_dir, send_email))
    process.start()
    print(f"线性执行风控任务，所有日志记录在第一个品种中：{log_path}")


# linear run backtest risk tasks
def linear_adapt_to_run_risk(checkpoint_path_list, data_dir, project_dir, send_email=False):
    # compare_result_list = list()
    # should_send_warn = False
    for checkpoint_path in checkpoint_path_list:
        # ToDo(hm): use_price_seq_file=True
        trade_result = adapt_to_run_risk(checkpoint_path, data_dir, project_dir, send_email)
        all_instances = get_instance_list(trade_result.param_list)
        actual_instance_df = merge_to_instance_df(instance_list=all_instances)
        save_path = join_path(checkpoint_path, PARAM_INSTANCE_DF_FILE_NAME)
        actual_instance_df.to_csv(save_path, index=False)
        save_path = join_path(checkpoint_path, PARAM_INSTANCE_CHART_FILE_NAME)
        draw_line(trade_result.price_list, instances=all_instances, time_list=trade_result.time_list,
                  save_path=save_path)
        price_seq_df = pd.DataFrame({TIME_COL: trade_result.time_list, PRICE_COL: trade_result.price_list})
        save_path = join_path(checkpoint_path, PRICE_TIME_SEQ_FILE_NAME)
        price_seq_df.to_csv(save_path, index=False)
        # compare
        # config_path = join_path(checkpoint_path, CONFIG_FILE_NAME)
        # run_config_dict = load_json(config_path)
        # backtest_start_time_str = run_config_dict[BACKTEST_START_TIME]
        # backtest_trade_dy_str = get_trade_day_str(pd.to_datetime(backtest_start_time_str))
        # account_name = run_config_dict[ACCOUNT]
        # record_dir = join_path(data_dir, RECORD_DIR, account_name)
        # cat = get_key_by_val(RISK_CAT_DICT, run_config_dict[CAT])
        # trade_day_to_df_dict_backtest = get_record_df_by_trade_day(record_dir, cat, True, print_load_info=True)
        # trade_day_to_df_dict_prod = get_record_df_by_trade_day(record_dir, cat, False, print_load_info=True)
        # is_equal, msg = compare_risk_record(trade_day_to_df_dict_prod, trade_day_to_df_dict_backtest,
        #                                     backtest_trade_dy_str)
        # result = f"品种{cat}在交易日{backtest_trade_dy_str}回测结果和线上"
        # if is_equal:
        #     result += "一致"
        # else:
        #     result += "不同：" + msg
        #     should_send_warn = True
        # print(result)
        # compare_result_list.append(result)
    if not send_email:
        return

    # log_path = join_path(checkpoint_path_list[0], RUN_LOG_FILE_NAME)
    # compare_result_list.append(f"运行日志参见：{log_path}")
    # content = "\n".join(compare_result_list)
    # if should_send_warn:
    #     send_warn_email(data_dir, "风控回测结果不一致", content)
    # else:
    #     send_info_email(data_dir, "风控回测结果一致", content)


def adapt_to_run_risk(checkpoint_path, data_dir, project_dir=None, send_email=True,
                      use_price_seq_file=False):
    config_path = join_path(checkpoint_path, CONFIG_FILE_NAME)
    run_config_dict = load_json(config_path)
    run_num = run_config_dict[RUN_NUM] + 1 if RUN_NUM in run_config_dict else 1
    # ToDo(hm): not PENDING, should add more status
    update_json_file(config_path, {STATUS: GOING_ON, PID: os.getpid(), RUN_NUM: run_num})
    account_name = run_config_dict[ACCOUNT]
    cat = run_config_dict[CAT]
    code = run_config_dict[CODE]
    use_api_code = USE_API_CODE in run_config_dict and run_config_dict[USE_API_CODE]
    param_df_path = join_path(checkpoint_path, PARAM_DF_FILE_NAME)
    param_df = pd.read_csv(param_df_path)
    param_list = list()
    param_version = run_config_dict[PARAM_VERSION]
    for row_idx, row in param_df.iterrows():
        param_code = f"{param_version}_{row_idx}"
        param = Param(
            code=param_code,
            cat=cat,
            account_name=account_name,
            contract_code=code,
            delta_time=row["tr"],
            # delta_percent 实际上要 /100
            delta_percent=row["dp"] / 100,
            alpha=row["al"],
            trade_mode=TradeMode(row["tm"])
        )
        param_list.append(param)
    account_config_path = join_path(checkpoint_path, ACCOUNT_CONFIG_FILE_NAME)
    if is_invalid_path(account_config_path):
        err_msg = f"账号{account_name}的账密文件加载失败：{account_config_path}"
        print_warn(err_msg)
        update_json_file(config_path,
                         {STATUS: FAILED, TASK_UPDATE_TIME: get_cur_time_str(READABLE_TIME_FORMAT),
                          MESSAGE: err_msg})
        if send_email:
            send_warn_email(data_dir, err_msg, content=f"代码版本: {get_code_version(project_dir)}")
        return

    account_dict = load_json(account_config_path)
    # risk_unit.run_risk(decrypt(account_dict[PASSWORD_G]), decrypt(account_dict[PASSWORD_T]),
    #                    decrypt(account_dict[ACCOUNT_G]), decrypt(account_dict[ACCOUNT_T]),
    #                    decrypt_simple(account_dict[BROKER]))
    if run_config_dict[DRY_RUN]:
        if use_api_code:
            try:
                code = get_code_from_tq(data_dir, account_name, cat)
            except RuntimeError as e:
                err_msg = f"账号{account_name}的{cat}{code}风控告警: API 获取合约代码失败，将会使用默认合约{code}"
                if send_email:
                    send_warn_email(data_dir, err_msg, content=f"代码版本: {get_code_version(project_dir)}")

        backtest_start_time = run_config_dict[BACKTEST_START_TIME]
        backtest_end_time = run_config_dict[BACKTEST_END_TIME]
        trade_input = TradeInput(param_list, code, user_name=decrypt(account_dict[ACCOUNT_T]),
                                 password=decrypt(account_dict[PASSWORD_T]), start_time=backtest_start_time,
                                 end_time=backtest_end_time)
        start_time = pd.to_datetime(trade_input.start_time)
        end_time = pd.to_datetime(trade_input.end_time)
        # use mock api
        if use_price_seq_file:
            price_seq_path = join_path(checkpoint_path, PRICE_TIME_SEQ_FILE_NAME)
            api = TqMockApi(data_path=price_seq_path, start_dt=start_time, end_dt=end_time)
            trade_executor = MockTradeExecutor(data_dir, 100)
        # use tq api
        else:
            tqacc = TqSim(trade_input.init_fund)
            print("账号密码：", trade_input.user_name, trade_input.password)
            api = TqApi(tqacc,
                        backtest=TqBacktest(start_dt=start_time, end_dt=end_time),
                        auth=TqAuth(trade_input.user_name, trade_input.password), web_gui=False)
            position = api.get_position(trade_input.code)
            trade_executor = TqTradeExecutor(data_dir, api, position)
        trade_result = run_risk(trade_input, api, trade_executor)
        return trade_result
    return None


def load_latest_account_cat_info_dict(data_dir, dry_run):
    account_cat_info_dict = dict()
    for account_name in ACCOUNT_NAME_LIST:
        account_cat_info_dict[account_name] = dict()
        for cat in CHOOSE_CAT_LIST:
            # every cat only run one process at the same time, so just find the latest one
            # /checkpoint/<account>/<cat>/<run_date_str>/<run_time_str>
            cur_process_info = get_latest_process_info(data_dir, account_name, cat, dry_run)
            account_cat_info_dict[account_name][cat] = cur_process_info
    return account_cat_info_dict


def get_latest_process_info(data_dir, account_name, cat, dry_run):
    cur_process_info = {
        ACCOUNT: account_name,
        CAT: cat,
        PID: None,
        CODE: None,
        STATUS: '',
        TASK_START_TIME: None,
        TASK_UPDATE_TIME: None,
        PATH: None,
        PARAM_VERSION: None,
        # RISK_START_TIME_LIST: None,
        INIT_FUND: None,
        OPEN_ONCE_MAX_FUND: None,
        TOTAL_FUND_FOR_ALL_INSTRUCT: None,
        DRY_RUN: None,
        RUN_NUM: None,
        MESSAGE: None,
    }
    ckp_path = get_latest_ckp_path(data_dir, account_name, cat, dry_run)
    cur_process_info[PATH] = ckp_path
    if is_invalid_path(ckp_path):
        return cur_process_info

    try:
        run_config_dict = load_config(ckp_path)
    except Exception as e:
        run_config_dict = None
    if is_empty(run_config_dict):
        return cur_process_info

    for key in cur_process_info:
        if key in run_config_dict:
            cur_process_info[key] = run_config_dict[key]

    cur_process_info[STATUS] = run_config_dict[STATUS]
    if PID in run_config_dict:
        cur_process_info[PID] = run_config_dict[PID]
        # update status by actual
        cur_process_info[STATUS] = get_actual_status(run_config_dict[STATUS], run_config_dict[PID])
        return cur_process_info

    return cur_process_info


def get_actual_status(raw_status, pid):
    is_running = is_process_running(pid)
    if raw_status == GOING_ON:
        if is_running:
            return GOING_ON

        return GOING_ON_OVER

    if raw_status == SUCCESS:
        if is_running:
            return SUCCESS

        return SUCCESS_OVER

    return raw_status


def get_latest_ckp_path(data_dir, account_name, cat, dry_run):
    if dry_run:
        cat_dir = join_path(data_dir, BACKTEST_CHECKPOINT_DIR, account_name, cat)
    else:
        cat_dir = join_path(data_dir, CHECKPOINT_DIR, account_name, cat)
    if is_invalid_path(cat_dir):
        return None

    run_date_list = os.listdir(cat_dir)
    if is_empty(run_date_list):
        return None

    latest_run_date = sorted(run_date_list)[-1]
    latest_run_date_dir = join_path(cat_dir, latest_run_date)
    run_time_list = os.listdir(latest_run_date_dir)
    if is_empty(run_time_list):
        return None

    latest_run_time = sorted(run_time_list)[-1]
    return join_path(latest_run_date_dir, latest_run_time)


# ToDo(hm): 实现重启任务，重新加载 instance，另外 instance 是不是应该实时的写到 run_config 中？
# ToDo(hm): 如果线上用这个该怎么修改呢？
# ToDo(hm): rename this
def run_risk(trade_input: TradeInput, api, trade_executor: BaseTradeExecutor) -> TradeResult:
    update_last_time = pd.to_datetime(trade_input.update_start_time)
    data_length = max([param.delta_time for param in trade_input.param_list]) + 1
    # 返回的K线序列数据是从当前最新一根K线开始往回取data_length根
    klines = api.get_kline_serial(trade_input.code, 1, data_length=data_length)
    trade_strategy = TradeStrategy(trade_executor, cd_ratio=trade_input.cd_ratio)
    # 记录所有开仓和平仓实例
    all_open_instances = list()
    all_close_instances = list()
    total_time_list = list()
    total_price_list = list()
    try:
        while True:
            # 等待行情更新
            is_updated = api.wait_update(deadline=time.time() + TQ_TIME_OUT)
            if not is_updated:
                print("No update received within timeout period")
                continue

            if not api.is_changing(klines):
                continue

            # 获取当前K线时间
            kline_time = pd.to_datetime(klines.iloc[-1]['datetime'] + 28800 * 1e9, unit='ns')
            time_delta = kline_time - update_last_time

            # 如果时间变化太小，跳过
            if time_delta.seconds < 1:
                continue

            # 更新上次处理时间
            update_last_time = kline_time
            if kline_time.minute % 5 == 0 and kline_time.second == 0:
                print(f"kline_time={kline_time}")

            # ToDo(hm): 区分线上和测试
            # if kline_time.second % 5 == 0:
            #     print(f"kline_time={kline_time}")

            # 获取时间和价格列表
            time_list, price_list = get_time_price_list(klines)

            if len(time_list) == 0 or len(price_list) == 0:
                print("Warning: Empty time or price list")
                continue

            # 存储所有时间价格数据
            if is_empty(total_time_list):
                total_time_list += time_list
                total_price_list += price_list
            else:
                for idx in range(len(time_list)):
                    # 找到第一个比 total_time_list[-1] 大的时间，并追加
                    if time_list[idx] > total_time_list[-1]:
                        total_time_list += time_list[idx:]
                        total_price_list += price_list[idx:]
                        break

            # 尝试开仓
            open_attempt_instance_list = trade_strategy.attempt_open_instance(trade_input.param_list, price_list,
                                                                              time_list)
            if open_attempt_instance_list:
                print(f"Opened {len(open_attempt_instance_list)} positions")
                for instance in open_attempt_instance_list:
                    print(
                        f"  - {instance.contract_code}: price={instance.open_price}, target={instance.expected_close_price}")
                all_open_instances.extend(open_attempt_instance_list)

            # 尝试平仓
            close_attempt_instance_list = trade_strategy.attempt_close_instance(trade_input.param_list, price_list,
                                                                                time_list)
            if close_attempt_instance_list:
                print(f"Closed {len(close_attempt_instance_list)} positions")
                for instance in close_attempt_instance_list:
                    print(
                        f"  - {instance.contract_code}: price={instance.actual_close_price}, profit={instance.profit}")
                all_close_instances.extend(close_attempt_instance_list)
        return TradeResult(trade_input.param_list, total_time_list, total_price_list)
    except KeyboardInterrupt:
        print("Trading interrupted by user")
    except BacktestFinished:
        print("Backtest finished")
        return TradeResult(trade_input.param_list, total_time_list, total_price_list)

    finally:
        # 关闭API连接
        api.close()


if __name__ == '__main__':
    # # 获取所有交易实例
    # all_instances = TradeStrategy.get_instance_list(param_list)
    #
    # # 计算总利润
    # total_profit = sum(instance.profit for instance in all_instances if instance.status == 'CLOSE_SUCCESS')
    # print(f"Total profit: {total_profit}")
    #
    # # 显示所有完成的交易
    # closed_instances = [instance for instance in all_instances if instance.status == 'CLOSE_SUCCESS']
    # if closed_instances:
    #     print("\nCompleted trades:")
    #     for idx, instance in enumerate(closed_instances, 1):
    #         print(
    #             f"  {idx}. {instance.code}: open={instance.open_price}, close={instance.actual_close_price}, profit={instance.profit}")
    #
    # draw_line(total_price_list, instances=all_instances, time_list=total_time_list)
    pass

    # 可以在这里自定义参数，或者使用默认参数

    # 示例1：使用默认参数运行
    # main()

    # 示例2：自定义参数运行
    # params = get_default_params()
    # main(param_list=params,
    #      start_time="2025-04-25 09:00:00",
    #      end_time="2025-04-25 15:00:00",
    #      cd_ratio=2.0,  # 减少冷却时间
    #      data_length=2000)  # 增加数据长度

    # 示例3
    # t_18-p_0.2-a_0.9-g_1s-m_1@1-v_6-op_5999-tp_6009.8-pp_5987-dp_12-dp%_0.20
